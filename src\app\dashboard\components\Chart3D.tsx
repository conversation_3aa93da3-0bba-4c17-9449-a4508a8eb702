import { Canvas } from '@react-three/fiber'
import { OrbitControls, Text } from '@react-three/drei'
import { useTheme } from '@mui/material'
import { useMemo, useRef } from 'react'
import * as THREE from 'three'

interface Chart3DProps {
  data: Array<{ name: string; value: number }>
  barColor?: string
  textColor?: string
  gridColor?: string
  width?: number
  height?: number
}

function Bar3D({ 
  position, 
  height, 
  color, 
  name, 
  value 
}: { 
  position: [number, number, number]
  height: number
  color: string
  name: string
  value: number
}) {
  const meshRef = useRef<THREE.Mesh>(null)
  
  return (
    <group>
      {/* Bar */}
      <mesh ref={meshRef} position={[position[0], height / 2, position[2]]}>
        <boxGeometry args={[0.8, height, 0.8]} />
        <meshStandardMaterial color={color} />
      </mesh>
      
      {/* Value label on top of bar */}
      <Text
        position={[position[0], height + 0.3, position[2]]}
        fontSize={0.2}
        color="#333"
        anchorX="center"
        anchorY="middle"
      >
        {value}%
      </Text>
      
      {/* Name label at base */}
      <Text
        position={[position[0], -0.5, position[2] + 0.5]}
        fontSize={0.15}
        color="#666"
        anchorX="center"
        anchorY="middle"
        rotation={[-Math.PI / 6, 0, 0]}
      >
        {name}
      </Text>
    </group>
  )
}

function Grid3D({ size = 5, divisions = 10, color = "#cccccc" }) {
  const points = useMemo(() => {
    const pts = []
    const step = size / divisions
    
    // Grid lines parallel to X axis
    for (let i = 0; i <= divisions; i++) {
      const z = -size / 2 + i * step
      pts.push(new THREE.Vector3(-size / 2, 0, z))
      pts.push(new THREE.Vector3(size / 2, 0, z))
    }
    
    // Grid lines parallel to Z axis
    for (let i = 0; i <= divisions; i++) {
      const x = -size / 2 + i * step
      pts.push(new THREE.Vector3(x, 0, -size / 2))
      pts.push(new THREE.Vector3(x, 0, size / 2))
    }
    
    return pts
  }, [size, divisions])

  return (
    <lineSegments>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={points.length}
          array={new Float32Array(points.flatMap(p => [p.x, p.y, p.z]))}
          itemSize={3}
        />
      </bufferGeometry>
      <lineBasicMaterial color={color} />
    </lineSegments>
  )
}

function YAxis({ maxValue = 100, color = "#666" }) {
  const ticks = useMemo(() => {
    const tickCount = 5
    const step = maxValue / tickCount
    return Array.from({ length: tickCount + 1 }, (_, i) => i * step)
  }, [maxValue])

  return (
    <group>
      {/* Y axis line */}
      <line>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={2}
            array={new Float32Array([0, 0, 0, 0, maxValue / 100 * 4, 0])}
            itemSize={3}
          />
        </bufferGeometry>
        <lineBasicMaterial color={color} />
      </line>
      
      {/* Y axis labels */}
      {ticks.map((tick, index) => (
        <Text
          key={index}
          position={[-0.5, (tick / 100) * 4, 0]}
          fontSize={0.12}
          color={color}
          anchorX="center"
          anchorY="middle"
        >
          {tick}%
        </Text>
      ))}
    </group>
  )
}

export default function Chart3D({ 
  data, 
  barColor = "#1976d2", 
  textColor = "#333",
  gridColor = "#e0e0e0",
  width = 400,
  height = 300
}: Chart3DProps) {
  const theme = useTheme()
  
  const normalizedData = useMemo(() => {
    return data.map((item, index) => ({
      ...item,
      position: [index * 1.5 - (data.length - 1) * 0.75, 0, 0] as [number, number, number],
      height: (item.value / 100) * 4 // Scale to reasonable 3D height
    }))
  }, [data])

  return (
    <div style={{ width: '100%', height: '100%', minHeight: height }}>
      <Canvas
        camera={{ 
          position: [5, 3, 5], 
          fov: 50 
        }}
        style={{ 
          background: theme.palette.mode === 'dark' ? '#1a1a1a' : '#fafafa' 
        }}
      >
        <ambientLight intensity={0.6} />
        <directionalLight position={[10, 10, 5]} intensity={0.8} />
        
        <Grid3D color={gridColor} />
        <YAxis color={textColor} />
        
        {normalizedData.map((item, index) => (
          <Bar3D
            key={index}
            position={item.position}
            height={item.height}
            color={barColor}
            name={item.name}
            value={item.value}
          />
        ))}
        
        <OrbitControls 
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          maxPolarAngle={Math.PI / 2}
          minDistance={3}
          maxDistance={15}
        />
      </Canvas>
    </div>
  )
}