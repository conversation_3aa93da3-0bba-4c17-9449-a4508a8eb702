/**
 * Tests for FullscreenChartCard component
 */

import { render, screen } from '@testing-library/react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import FullscreenChartCard from '../FullscreenChartCard'
import { mockTasks } from '../../../../__mocks__/mockData'

// Mock recharts components
jest.mock('recharts', () => ({
	ResponsiveContainer: ({ children }: { children: React.ReactNode }) => (
		<div data-testid="responsive-container">{children}</div>
	),
	BarChart: ({ children }: { children: React.ReactNode }) => (
		<div data-testid="bar-chart">{children}</div>
	),
	Bar: () => <div data-testid="bar" />,
	CartesianGrid: () => <div data-testid="cartesian-grid" />,
	XAxis: () => <div data-testid="x-axis" />,
	YAxis: () => <div data-testid="y-axis" />,
	Tooltip: () => <div data-testid="tooltip" />,
}))

// Mock react-i18next
jest.mock('react-i18next', () => ({
	useTranslation: () => ({
		t: (key: string) => {
			const translations: Record<string, string> = {
				'chart.progress': 'Progress',
				'table.type': 'Type',
				'table.start': 'Start',
				'table.end': 'End',
				'table.duration': 'Duration',
			}
			return translations[key] || key
		},
	}),
}))

// Mock alert colors utility
jest.mock('../../../../common/utils/alertColors', () => ({
	getAlertColors: () => ({
		header: '#1976d2',
		background: '#e3f2fd',
		row: '#f5f5f5',
		progressRate: '#4caf50',
	}),
}))

const theme = createTheme()

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
	<ThemeProvider theme={theme}>{children}</ThemeProvider>
)

describe('FullscreenChartCard', () => {
	const mockTask = mockTasks[0]

	describe('rendering', () => {
		it('should render task information section', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByText(mockTask.name)).toBeInTheDocument()
			expect(screen.getByText(`Progress: ${mockTask.progressRate}%`)).toBeInTheDocument()
			expect(screen.getByText('Time Analysis')).toBeInTheDocument()
		})

		it('should render time analysis table', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByText('Type')).toBeInTheDocument()
			expect(screen.getByText('Start')).toBeInTheDocument()
			expect(screen.getByText('End')).toBeInTheDocument()
		})

		it('should render planned and actual time data', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByText('Planned')).toBeInTheDocument()
			expect(screen.getByText('Actual')).toBeInTheDocument()
			expect(screen.getByText(mockTask.plannedStart)).toBeInTheDocument()
			expect(screen.getByText(mockTask.plannedEnd)).toBeInTheDocument()
			expect(screen.getByText(mockTask.actualStart)).toBeInTheDocument()
			expect(screen.getByText(mockTask.actualEnd)).toBeInTheDocument()
		})

		it('should render chart section', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByText('Progress Chart')).toBeInTheDocument()
			expect(screen.getByTestId('responsive-container')).toBeInTheDocument()
			expect(screen.getByTestId('bar-chart')).toBeInTheDocument()
			expect(screen.getByTestId('bar')).toBeInTheDocument()
		})

		it('should render chart components', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByTestId('cartesian-grid')).toBeInTheDocument()
			expect(screen.getByTestId('x-axis')).toBeInTheDocument()
			expect(screen.getByTestId('y-axis')).toBeInTheDocument()
			expect(screen.getByTestId('tooltip')).toBeInTheDocument()
		})
	})

	describe('layout', () => {
		it('should have proper layout structure', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			const mainContainer = screen.getByText(mockTask.name).closest('[class*="MuiBox-root"]')
			expect(mainContainer).toBeInTheDocument()
		})

		it('should have task information and chart sections', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			// Task information section
			expect(screen.getByText('Time Analysis')).toBeInTheDocument()
			// Chart section
			expect(screen.getByText('Progress Chart')).toBeInTheDocument()
		})
	})

	describe('data display', () => {
		it('should display correct progress rate', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByText(`Progress: ${mockTask.progressRate}%`)).toBeInTheDocument()
		})

		it('should display task name as heading', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			const taskNameHeading = screen.getByRole('heading', { name: mockTask.name })
			expect(taskNameHeading).toBeInTheDocument()
		})

		it('should display time data in table format', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			const table = screen.getByRole('table')
			expect(table).toBeInTheDocument()

			// Check that both planned and actual rows are present
			const rows = screen.getAllByRole('row')
			expect(rows).toHaveLength(3) // Header + Planned + Actual
		})
	})

	describe('memoization', () => {
		it('should not re-render when task props are the same', () => {
			// Arrange
			const { rerender } = render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)
			const initialChart = screen.getByTestId('bar-chart')

			// Act
			rerender(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByTestId('bar-chart')).toBe(initialChart)
		})

		it('should re-render when task changes', () => {
			// Arrange
			const { rerender } = render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			const newTask = { ...mockTask, name: 'Updated Task', progressRate: 75 }

			// Act
			rerender(
				<TestWrapper>
					<FullscreenChartCard selectedTask={newTask} />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByText('Updated Task')).toBeInTheDocument()
			expect(screen.getByText('Progress: 75%')).toBeInTheDocument()
		})
	})

	describe('accessibility', () => {
		it('should have proper heading structure', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByRole('heading', { name: mockTask.name })).toBeInTheDocument()
			expect(screen.getByRole('heading', { name: 'Time Analysis' })).toBeInTheDocument()
			expect(screen.getByRole('heading', { name: 'Progress Chart' })).toBeInTheDocument()
		})

		it('should have proper table structure', () => {
			// Arrange & Act
			render(
				<TestWrapper>
					<FullscreenChartCard selectedTask={mockTask} />
				</TestWrapper>,
			)

			// Assert
			expect(screen.getByRole('table')).toBeInTheDocument()
			expect(screen.getAllByRole('columnheader')).toHaveLength(3)
			expect(screen.getAllByRole('row')).toHaveLength(3) // Header + 2 data rows
		})
	})
})
