/**
 * Tests for settings utility functions
 */

import {
	validateSettings,
	exportSettings,
	importSettings,
	resetSettings,
	DEFAULT_SETTINGS,
} from '../settings'
import { DEFAULT_TASK_STATE_COLORS } from '../../../common/utils/taskStateColors'

describe('Settings Utilities', () => {
	describe('validateSettings', () => {
		it('should return default settings for empty input', () => {
			const result = validateSettings({})
			expect(result).toEqual(DEFAULT_SETTINGS)
		})

		it('should validate locale correctly', () => {
			const result = validateSettings({ locale: 'ja' })
			expect(result.locale).toBe('ja')
		})

		it('should fallback to default for invalid locale', () => {
			const result = validateSettings({ locale: 'invalid' as never })
			expect(result.locale).toBe(DEFAULT_SETTINGS.locale)
		})

		it('should validate themeMode correctly', () => {
			const result = validateSettings({ themeMode: 'dark' })
			expect(result.themeMode).toBe('dark')
		})

		it('should fallback to default for invalid themeMode', () => {
			const result = validateSettings({
				themeMode: 'invalid',
			} as unknown as { themeMode?: 'dark' | 'light' })
			expect(result.themeMode).toBe(DEFAULT_SETTINGS.themeMode)
		})

		it('should validate fontScale within range', () => {
			const result = validateSettings({ fontScale: 1.2 })
			expect(result.fontScale).toBe(1.2)
		})

		it('should fallback to default for fontScale out of range', () => {
			const result = validateSettings({ fontScale: 2.0 })
			expect(result.fontScale).toBe(DEFAULT_SETTINGS.fontScale)
		})

		it('should fallback to default for invalid fontScale type', () => {
			const result = validateSettings({ fontScale: 'invalid' as never })
			expect(result.fontScale).toBe(DEFAULT_SETTINGS.fontScale)
		})

		it('should validate taskStateColors correctly', () => {
			const colors = {
				normal: '#123456',
				working: '#ABCDEF',
				completed: '#000000',
				delay: '#FFFFFF',
				interrupt: '#FF00FF',
			}
			const result = validateSettings({ taskStateColors: colors })
			expect(result.taskStateColors).toEqual(colors)
		})

		it('should fallback to default for invalid taskStateColors', () => {
			const result = validateSettings({ taskStateColors: { normal: 'invalid' } as any })
			expect(result.taskStateColors).toEqual(DEFAULT_TASK_STATE_COLORS)
		})
	})

	describe('exportSettings', () => {
		it('should export settings as JSON string', () => {
			const settings = {
				locale: 'ja' as const,
				themeMode: 'dark' as const,
				fontScale: 1.1,
				taskStateColors: DEFAULT_TASK_STATE_COLORS,
			}
			const result = exportSettings(settings)
			const parsed = JSON.parse(result)
			expect(parsed).toEqual(settings)
		})
	})

	describe('importSettings', () => {
		it('should import valid settings JSON', () => {
			const settings = {
				locale: 'ja' as const,
				themeMode: 'dark' as const,
				fontScale: 1.1,
				taskStateColors: DEFAULT_TASK_STATE_COLORS,
			}
			const json = JSON.stringify(settings)
			const result = importSettings(json)
			expect(result).toEqual(settings)
		})

		it('should validate imported settings', () => {
			const invalidSettings = { locale: 'invalid', themeMode: 'dark', fontScale: 1.1 }
			const json = JSON.stringify(invalidSettings)
			const result = importSettings(json)
			expect(result.locale).toBe(DEFAULT_SETTINGS.locale)
			expect(result.themeMode).toBe('dark')
			expect(result.fontScale).toBe(1.1)
		})

		it('should throw error for invalid JSON', () => {
			expect(() => importSettings('invalid json')).toThrow('Invalid settings format')
		})
	})

	describe('resetSettings', () => {
		it('should return default settings', () => {
			const result = resetSettings()
			expect(result).toEqual(DEFAULT_SETTINGS)
		})

		it('should return a new object instance', () => {
			const result = resetSettings()
			expect(result).not.toBe(DEFAULT_SETTINGS)
		})
	})
})
