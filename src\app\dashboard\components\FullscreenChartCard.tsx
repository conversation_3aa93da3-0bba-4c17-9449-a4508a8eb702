import {
	Typography,
	useTheme,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Box,
	Card,
	CardContent,
} from '@mui/material'
import {
	Bar,
	BarChart,
	CartesianGrid,
	ResponsiveContainer,
	Tooltip as RechartsTooltip,
	<PERSON>Axis,
	<PERSON>Axis,
} from 'recharts'
import type { Task } from '../../../models/Task'
import { useTranslation } from 'react-i18next'
import { memo } from 'react'
import { getAlertColors } from '../../../common/utils/alertColors'

function areTasksEqual(prev: Task | null, next: Task | null): boolean {
	if (prev === next) return true
	if (!prev || !next) return false
	return (
		prev.id === next.id &&
		prev.name === next.name &&
		prev.progressRate === next.progressRate &&
		prev.plannedStart === next.plannedStart &&
		prev.plannedEnd === next.plannedEnd &&
		prev.plannedDuration === next.plannedDuration &&
		prev.actualStart === next.actualStart &&
		prev.actualEnd === next.actualEnd &&
		prev.actualDuration === next.actualDuration &&
		JSON.stringify(prev.alerts) === JSON.stringify(next.alerts)
	)
}

interface FullscreenChartCardProps {
	selectedTask: Task
}

function FullscreenChartCardComponent({ selectedTask }: FullscreenChartCardProps) {
	const theme = useTheme()
	const { t } = useTranslation()

	const alertColors = getAlertColors(selectedTask.alerts)
	const data = [{ name: selectedTask.name, value: selectedTask.progressRate }]

	const timeData = [
		{
			type: 'Planned',
			start: selectedTask.plannedStart,
			end: selectedTask.plannedEnd,
			duration: selectedTask.plannedDuration,
		},
		{
			type: 'Actual',
			start: selectedTask.actualStart,
			end: selectedTask.actualEnd,
			duration: selectedTask.actualDuration,
		},
	]

	return (
		<Box
			className="w-full h-full gap-1.5"
			sx={{
				height: 'calc(100dvh - 180px)',
				backgroundColor: theme.palette.background.default,
				display: 'flex',
				flexDirection: { xs: 'column', md: 'row' },
			}}
		>
			{/* Task Information Section */}
			<Box sx={{ flex: { xs: '1', md: '0 0 33%' } }}>
				<Card
					elevation={3}
					sx={{
						height: '100%',
					}}
				>
					<CardContent
						sx={{
							height: '100%',
							display: 'flex',
							flexDirection: 'column',
						}}
					>
						<Typography variant="h5" fontWeight={700} gutterBottom>
							{selectedTask.name}
						</Typography>
						<Typography
							variant="h3"
							fontWeight={600}
							sx={{
								mb: 2,
								color: getAlertColors(selectedTask.alerts).progressRate,
							}}
						>
							{t('chart.progress')}: {selectedTask.progressRate}%
						</Typography>

						<Box sx={{ flexGrow: 1 }}>
							<Typography variant="h6" fontWeight={600} gutterBottom>
								Time Analysis
							</Typography>
							<TableContainer>
								<Table sx={{ backgroundColor: '#fff' }}>
									<TableHead>
										<TableRow>
											<TableCell
												align="center"
												sx={{
													fontWeight: 600,
													backgroundColor: alertColors.header,
													color: '#fff',
												}}
											>
												{t('table.type')}
											</TableCell>
											<TableCell
												align="center"
												sx={{
													fontWeight: 600,
													backgroundColor: alertColors.header,
													color: '#fff',
												}}
											>
												Start
											</TableCell>
											<TableCell
												align="center"
												sx={{
													fontWeight: 600,
													backgroundColor: alertColors.header,
													color: '#fff',
												}}
											>
												End
											</TableCell>
										</TableRow>
									</TableHead>
									<TableBody>
										{timeData.map((item, index) => (
											<TableRow
												key={index}
												sx={{
													backgroundColor: alertColors.row,
													'&:hover': {
														backgroundColor: alertColors.row,
														filter: 'brightness(0.95)',
													},
												}}
											>
												<TableCell align="center" sx={{ fontWeight: 600, cursor: 'default' }}>
													{item.type}
												</TableCell>
												<TableCell align="center" sx={{cursor: 'default'}}>{item.start}</TableCell>
												<TableCell align="center" sx={{cursor: 'default'}}>{item.end}</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</TableContainer>
						</Box>
					</CardContent>
				</Card>
			</Box>

			{/* Chart Section */}
			<Box sx={{ flex: { xs: '1', md: '0 0 67%' } }}>
				<Card
					elevation={3}
					sx={{
						height: '100%',
					}}
				>
					<CardContent
						sx={{
							height: '100%',
							display: 'flex',
							flexDirection: 'column',
						}}
					>
						<Typography variant="h6" fontWeight={600} gutterBottom>
							Progress Chart
						</Typography>
						<Box
							sx={{
								flexGrow: 1,
								display: 'flex',
								alignItems: 'center',
								justifyContent: 'center',
							}}
						>
							<ResponsiveContainer width="100%" height="100%">
								<BarChart
									data={data}
									margin={{
										top: 20,
										right: 30,
										left: 20,
										bottom: 5,
									}}
								>
									<CartesianGrid strokeDasharray="3 3" />
									<XAxis dataKey="name" />
									<YAxis domain={[0, 100]} />
									<RechartsTooltip
										formatter={(value) => [`${value}%`, 'Progress']}
									/>
									<Bar
										dataKey="value"
										fill={alertColors.background}
										radius={[8, 8, 0, 0]}
										stroke={alertColors.header}
										strokeWidth={2}
									/>
								</BarChart>
							</ResponsiveContainer>
						</Box>
					</CardContent>
				</Card>
			</Box>
		</Box>
	)
}

const FullscreenChartCard = memo(FullscreenChartCardComponent, (prev, next) =>
	areTasksEqual(prev.selectedTask, next.selectedTask),
)

export default FullscreenChartCard
