/**
 * Tests for FullscreenDataTable component
 */

import { render, screen, fireEvent } from '@testing-library/react'
import FullscreenDataTable from '../FullscreenDataTable'
import { mockTasks } from '../../../../__mocks__/mockData'

// Mock react-i18next
jest.mock('react-i18next', () => ({
	useTranslation: () => ({
		t: (key: string) => {
			const translations: Record<string, string> = {
				'table.planned': 'Planned',
				'table.actual': 'Actual',
				'table.duration': 'Duration',
				'table.no': 'No',
				'table.shippingDate': 'Shipping Date',
				'table.vangp': 'Van GP',
				'table.deliveryTime': 'Delivery Time',
				'table.start': 'Start',
				'table.end': 'End',
			}
			return translations[key] || key
		},
	}),
}))

// Mock alert colors utility
jest.mock('../../../../common/utils/alertColors', () => ({
	getAlertColors: () => ({
		header: '#1976d2',
		background: '#e3f2fd',
		row: '#f5f5f5',
		progressRate: '#4caf50',
	}),
}))

describe('FullscreenDataTable', () => {
	const defaultProps = {
		rows: mockTasks,
		selectedRowId: null,
		onSelect: jest.fn(),
	}

	beforeEach(() => {
		jest.clearAllMocks()
	})

	describe('rendering', () => {
		it('should render table with correct headers', () => {
			// Arrange & Act
			render(<FullscreenDataTable {...defaultProps} />)

			// Assert
			expect(screen.getAllByText('Planned')).toHaveLength(1) // Main header and duration column
			expect(screen.getAllByText('Actual')).toHaveLength(1) // Main header and duration column
		})

		it('should render table with correct sub-headers', () => {
			// Arrange & Act
			render(<FullscreenDataTable {...defaultProps} />)

			// Assert
			expect(screen.getByText('Shipping Date')).toBeInTheDocument()
			expect(screen.getByText('Van GP')).toBeInTheDocument()
			expect(screen.getByText('Delivery Time')).toBeInTheDocument()
			expect(screen.getAllByText('Start')).toHaveLength(2) // Planned and Actual
			expect(screen.getAllByText('End')).toHaveLength(2) // Planned and Actual
		})

		it('should render all task rows', () => {
			// Arrange & Act
			render(<FullscreenDataTable {...defaultProps} />)

			// Assert
			mockTasks.forEach((task) => {
				expect(screen.getByText(task.rowName)).toBeInTheDocument()
				expect(screen.getByText(task.shippingDate)).toBeInTheDocument()
				expect(screen.getByText(task.vangp)).toBeInTheDocument()
				expect(screen.getByText(task.deliveryTime)).toBeInTheDocument()
			})
		})

		it('should render empty table when no rows provided', () => {
			// Arrange & Act
			render(<FullscreenDataTable {...defaultProps} rows={[]} />)

			// Assert
			expect(screen.getAllByText('Planned')).toHaveLength(1) // Main header and duration column
			expect(screen.getAllByText('Actual')).toHaveLength(1) // Main header and duration column
			// No task data should be present
			expect(screen.queryByText('Task Alpha')).not.toBeInTheDocument()
		})
	})

	describe('row selection', () => {
		it('should call onSelect when row is clicked', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			render(<FullscreenDataTable {...defaultProps} onSelect={mockOnSelect} />)

			// Act
			const firstRow = screen.getByText(mockTasks[0].rowName).closest('tr')
			if (firstRow) {
				fireEvent.click(firstRow)
			}

			// Assert
			expect(mockOnSelect).toHaveBeenCalledTimes(1)
			expect(mockOnSelect).toHaveBeenCalledWith(mockTasks[0])
		})

		it('should not call onSelect when onSelect is not provided', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			render(<FullscreenDataTable {...defaultProps} onSelect={undefined} />)

			// Act
			const firstRow = screen.getByText(mockTasks[0].rowName).closest('tr')
			if (firstRow) {
				fireEvent.click(firstRow)
			}

			// Assert
			expect(mockOnSelect).not.toHaveBeenCalled()
		})

		it('should highlight selected row', () => {
			// Arrange
			render(<FullscreenDataTable {...defaultProps} selectedRowId={mockTasks[0].id} />)

			// Act
			const selectedRow = screen.getByText(mockTasks[0].rowName).closest('tr')

			// Assert
			expect(selectedRow).toHaveClass('Mui-selected')
		})
	})

	describe('accessibility', () => {
		it('should have proper table structure', () => {
			// Arrange & Act
			render(<FullscreenDataTable {...defaultProps} />)

			// Assert
			expect(screen.getByRole('table')).toBeInTheDocument()
			expect(screen.getAllByRole('columnheader')).toHaveLength(13) // All header cells including colspan headers
			expect(screen.getAllByRole('row')).toHaveLength(mockTasks.length + 2) // Data rows + 2 header rows
		})

		it('should have clickable rows when onSelect is provided', () => {
			// Arrange & Act
			render(<FullscreenDataTable {...defaultProps} />)

			// Assert
			const dataRows = screen.getAllByRole('row').slice(2) // Skip header rows
			dataRows.forEach((row) => {
				expect(row).toHaveStyle('cursor: pointer')
			})
		})

		it('should not have clickable rows when onSelect is not provided', () => {
			// Arrange & Act
			render(<FullscreenDataTable {...defaultProps} onSelect={undefined} />)

			// Assert
			const dataRows = screen.getAllByRole('row').slice(2) // Skip header rows
			dataRows.forEach((row) => {
				expect(row).toHaveStyle('cursor: default')
			})
		})
	})

	describe('memoization', () => {
		it('should not re-render when props are the same', () => {
			// Arrange
			const { rerender } = render(<FullscreenDataTable {...defaultProps} />)
			const initialTable = screen.getByRole('table')

			// Act
			rerender(<FullscreenDataTable {...defaultProps} />)

			// Assert
			expect(screen.getByRole('table')).toBe(initialTable)
		})

		it('should re-render when rows change', () => {
			// Arrange
			const { rerender } = render(<FullscreenDataTable {...defaultProps} />)
			const newTasks = [mockTasks[0]]

			// Act
			rerender(<FullscreenDataTable {...defaultProps} rows={newTasks} />)

			// Assert
			expect(screen.getByText(mockTasks[0].rowName)).toBeInTheDocument()
			expect(screen.queryByText(mockTasks[1].rowName)).not.toBeInTheDocument()
		})

		it('should re-render when selectedRowId changes', () => {
			// Arrange
			const { rerender } = render(<FullscreenDataTable {...defaultProps} />)

			// Act
			rerender(<FullscreenDataTable {...defaultProps} selectedRowId={mockTasks[0].id} />)

			// Assert
			const selectedRow = screen.getByText(mockTasks[0].rowName).closest('tr')
			expect(selectedRow).toHaveClass('Mui-selected')
		})
	})
})
