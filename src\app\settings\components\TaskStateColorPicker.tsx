import { Box, Typo<PERSON>, Text<PERSON>ield, Button, Paper, Divider } from '@mui/material'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { type TaskStateColorSettings } from '../../../common/utils/taskStateColors'

interface TaskStateColorPickerProps {
	colors: TaskStateColorSettings
	onChange: (colors: TaskStateColorSettings) => void
	onReset: () => void
}

interface ColorInputProps {
	label: string
	value: string
	onChange: (value: string) => void
}

function ColorInput({ label, value, onChange }: ColorInputProps) {
	const [inputValue, setInputValue] = useState(value)

	const handleChange = (newValue: string) => {
		setInputValue(newValue)
		// Validate hex color format
		if (/^#[0-9A-Fa-f]{6}$/.test(newValue)) {
			onChange(newValue)
		}
	}

	const handleBlur = () => {
		// Reset to valid value if invalid
		if (!/^#[0-9A-Fa-f]{6}$/.test(inputValue)) {
			setInputValue(value)
		}
	}

	return (
		<Paper sx={{ mb: 2 }}>
			<Typography variant="subtitle2" sx={{ mb: 0.5 }}>
				{label}
			</Typography>
			<Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
				<Box
					data-testid={`color-preview-${label.toLowerCase().replace(/\s+/g, '-')}`}
					sx={{
						width: 40,
						height: 40,
						backgroundColor: value,
						border: '1px solid #ccc',
						borderRadius: 1,
						flexShrink: 0,
					}}
				/>
				<Box sx={{ flex: 1 }}>
					<TextField
						size="small"
						value={inputValue}
						onChange={(e) => handleChange(e.target.value)}
						onBlur={handleBlur}
						placeholder="#FFFFFF"
						inputProps={{
							pattern: '^#[0-9A-Fa-f]{6}$',
							maxLength: 7,
						}}
						sx={{ width: 120 }}
					/>
				</Box>
			</Box>
		</Paper>
	)
}

export default function TaskStateColorPicker({
	colors,
	onChange,
	onReset,
}: TaskStateColorPickerProps) {
	const { t } = useTranslation()

	const handleColorChange = (state: keyof TaskStateColorSettings, color: string) => {
		onChange({
			...colors,
			[state]: color,
		})
	}

	const colorConfigs = [
		{
			key: 'normal' as const,
			label: t('settings.colors.normal', { defaultValue: 'Normal' }),
		},
		{
			key: 'working' as const,
			label: t('settings.colors.working', { defaultValue: 'Working' }),
		},
		{
			key: 'completed' as const,
			label: t('settings.colors.completed', { defaultValue: 'Completed' }),
		},
		{
			key: 'delay' as const,
			label: t('settings.colors.delay', { defaultValue: 'Delay' }),
		},
		{
			key: 'interrupt' as const,
			label: t('settings.colors.interrupt', { defaultValue: 'Interrupt' }),
		},
	]

	return (
		<Box sx={{ p: 3 }}>
			<Box
				sx={{
					display: 'flex',
					justifyContent: 'space-between',
					alignItems: 'center',
					mb: 3,
				}}
			>
				<Typography variant="h6">
					{t('settings.colors.title', { defaultValue: 'Table Row Colors' })}
				</Typography>
			</Box>

			<Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
				{colorConfigs.map((config) => (
					<ColorInput
						key={config.key}
						label={config.label}
						value={colors[config.key]}
						onChange={(color) => handleColorChange(config.key, color)}
					/>
				))}
			</Box>

			<Button variant="outlined" size="small" onClick={onReset}>
				{t('settings.colors.reset', { defaultValue: 'Reset to Default' })}
			</Button>
			<Divider sx={{ my: 3 }} />

			<Typography variant="body2" color="text.secondary">
				<strong>{t('settings.colors.note', { defaultValue: 'Note:' })}</strong>{' '}
				{t('settings.colors.noteText', {
					defaultValue:
						'These colors only apply to table rows. Header colors are determined by alert status and remain unchanged.',
				})}
			</Typography>
		</Box>
	)
}
