import { AppBar, Too<PERSON>bar, Typo<PERSON>, Button } from '@mui/material'
import { Link as RouterLink } from 'react-router-dom'
import { useTranslation } from 'react-i18next'

export default function Header() {
	const { t } = useTranslation()

	return (
		<AppBar position="sticky" sx={{ top: 0 }}>
			<Toolbar variant="dense">
				<Typography variant="h6" sx={{ flexGrow: 1, fontSize: 16 }}>
					VMApp
				</Typography>
				<Button component={RouterLink} to="/" color="inherit">
					{t('nav.dashboard')}
				</Button>
				<Button component={RouterLink} to="/settings" color="inherit">
					{t('nav.settings')}
				</Button>
			</Toolbar>
		</AppBar>
	)
}
