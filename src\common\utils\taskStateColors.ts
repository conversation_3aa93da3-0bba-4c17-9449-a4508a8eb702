import type { Task } from '../../models/Task'

/**
 * Task state enumeration for table row coloring
 */
export const TaskState = {
	NORMAL: 'normal',
	WORKING: 'working',
	COMPLETED: 'completed',
	DELAY: 'delay',
	INTERRUPT: 'interrupt',
} as const

export type TaskState = (typeof TaskState)[keyof typeof TaskState]

/**
 * Color palette for task states
 */
export const TASK_STATE_COLORS = {
	[TaskState.NORMAL]: '#FFFFFF',
	[TaskState.WORKING]: '#FFFF00',
	[TaskState.COMPLETED]: '#D0D0D0',
	[TaskState.DELAY]: '#FF0000',
	[TaskState.INTERRUPT]: '#CAEDFB',
} as const

/**
 * Default color palette settings
 */
export interface TaskStateColorSettings {
	normal: string
	working: string
	completed: string
	delay: string
	interrupt: string
}

export const DEFAULT_TASK_STATE_COLORS: TaskStateColorSettings = {
	normal: TASK_STATE_COLORS[TaskState.NORMAL],
	working: TASK_STATE_COLORS[TaskState.WORKING],
	completed: TASK_STATE_COLORS[TaskState.COMPLETED],
	delay: TASK_STATE_COLORS[TaskState.DELAY],
	interrupt: TASK_STATE_COLORS[TaskState.INTERRUPT],
}

/**
 * Validates task state color settings
 */
export const validateTaskStateColors = (
	colors: Partial<TaskStateColorSettings>,
): TaskStateColorSettings => {
	const isValidColor = (color: unknown): color is string => {
		return typeof color === 'string' && /^#[0-9A-Fa-f]{6}$/.test(color)
	}

	return {
		normal: isValidColor(colors.normal) ? colors.normal : DEFAULT_TASK_STATE_COLORS.normal,
		working: isValidColor(colors.working) ? colors.working : DEFAULT_TASK_STATE_COLORS.working,
		completed: isValidColor(colors.completed)
			? colors.completed
			: DEFAULT_TASK_STATE_COLORS.completed,
		delay: isValidColor(colors.delay) ? colors.delay : DEFAULT_TASK_STATE_COLORS.delay,
		interrupt: isValidColor(colors.interrupt)
			? colors.interrupt
			: DEFAULT_TASK_STATE_COLORS.interrupt,
	}
}

/**
 * Determines the task state based on task data
 * Priority: delay > completed > working > normal
 */
export const getTaskState = (task: Task): TaskState => {
	// Check for delay state first (highest priority)
	// Delay state: valid alerts present in the task data
	if (task.alerts && task.alerts.length > 0 && task.alerts.some((alert) => alert > 0)) {
		return TaskState.DELAY
	}

	// Check for completed state
	// Completed: actual data has end time
	if (task.actualEnd && task.actualEnd.trim() !== '') {
		return TaskState.COMPLETED
	}

	// Check for working state
	// Working: actual data has start time
	if (task.actualStart && task.actualStart.trim() !== '') {
		return TaskState.WORKING
	}

	// Default to normal state
	// Normal: actual data has no start time
	return TaskState.NORMAL
}

/**
 * Gets the color for a task based on its state and color settings
 */
export const getTaskStateColor = (task: Task, colorSettings: TaskStateColorSettings): string => {
	const state = getTaskState(task)
	return colorSettings[state]
}

/**
 * Interface for task state colors used in table components
 */
export interface TaskStateColors {
	background: string
	header: string
	row: string
	progressRate: string
}

/**
 * Gets task state colors for table display, combining state-based row colors with alert-based header colors
 * This maintains compatibility with existing alert color system while adding state-based row colors
 */
export const getTaskStateColors = (
	task: Task,
	colorSettings: TaskStateColorSettings,
): TaskStateColors => {
	// Get state-based row color
	const rowColor = getTaskStateColor(task, colorSettings)

	// Keep existing alert-based header colors for consistency
	const hasAlert = Array.isArray(task.alerts)
		? task.alerts.some((alert) => alert === 1 || alert === 2)
		: task.alerts === 1

	if (hasAlert) {
		return {
			background: '#9C27B0', // Purple base
			header: '#7B1FA2', // Darker purple for header
			row: rowColor, // State-based row color
			progressRate: '#e6172f',
		}
	}

	return {
		background: '#2196F3', // Blue base
		header: '#1976D2', // Darker blue for header
		row: rowColor, // State-based row color
		progressRate: '#00e676',
	}
}
